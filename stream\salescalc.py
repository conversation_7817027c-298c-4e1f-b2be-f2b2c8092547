import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, date
import time

# Page configuration
st.set_page_config(
    page_title="SuperMart Analytics Dashboard",
    page_icon="🛒",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for enhanced styling
st.markdown("""
<style>
    /* Main background and theme */
    .main {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 0rem 1rem;
    }

    /* Custom header styling */
    .custom-header {
        background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .custom-header h1 {
        color: white;
        font-size: 3rem;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin: 0;
    }

    .custom-header p {
        color: white;
        font-size: 1.2rem;
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }

    /* Card styling */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border-left: 5px solid #4ECDC4;
        margin: 1rem 0;
        transition: transform 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.5rem 2rem;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }

    /* Input field styling */
    .stTextInput > div > div > input,
    .stNumberInput > div > div > input,
    .stSelectbox > div > div > select {
        border-radius: 10px;
        border: 2px solid #e0e0e0;
        transition: border-color 0.3s ease;
    }

    .stTextInput > div > div > input:focus,
    .stNumberInput > div > div > input:focus,
    .stSelectbox > div > div > select:focus {
        border-color: #4ECDC4;
        box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
    }

    /* Success/Error message styling */
    .stSuccess {
        background: linear-gradient(45deg, #4ECDC4, #44A08D);
        border-radius: 10px;
        animation: slideIn 0.5s ease;
    }

    .stError {
        background: linear-gradient(45deg, #FF6B6B, #FF8E53);
        border-radius: 10px;
        animation: slideIn 0.5s ease;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-20px); }
        to { opacity: 1; transform: translateX(0); }
    }

    /* Data table styling */
    .dataframe {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    /* Chart container styling */
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }

    /* Navigation styling */
    .nav-item {
        padding: 0.5rem 1rem;
        margin: 0.2rem 0;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .nav-item:hover {
        background: rgba(255,255,255,0.1);
        transform: translateX(5px);
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state for storing sales data
if 'sales_data' not in st.session_state:
    st.session_state.sales_data = pd.DataFrame(columns=[
        'Date', 'Product_Name', 'Category', 'Quantity', 'Unit_Price', 'Total_Amount'
    ])

def add_sale_entry(date, product_name, category, quantity, unit_price):
    """Add a new sale entry to the session state"""
    total_amount = quantity * unit_price
    new_entry = pd.DataFrame({
        'Date': [date],
        'Product_Name': [product_name],
        'Category': [category],
        'Quantity': [quantity],
        'Unit_Price': [unit_price],
        'Total_Amount': [total_amount]
    })
    st.session_state.sales_data = pd.concat([st.session_state.sales_data, new_entry], ignore_index=True)

def main():
    # Custom animated header
    st.markdown("""
    <div class="custom-header">
        <h1>🛒 SuperMart Analytics Dashboard</h1>
        <p>✨ Your Complete Sales Management & Analytics Solution ✨</p>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced sidebar with icons and styling
    st.sidebar.markdown("""
    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px; margin-bottom: 1rem;">
        <h2 style="color: white; margin: 0;">🧭 Navigation</h2>
        <p style="color: rgba(255,255,255,0.8); margin: 0.5rem 0 0 0;">Choose your destination</p>
    </div>
    """, unsafe_allow_html=True)

    # Navigation with icons
    page_options = {
        "📝 Data Entry": "Data Entry",
        "📊 Sales Report": "Sales Report",
        "📈 Statistical Analysis": "Statistical Analysis"
    }

    selected_page = st.sidebar.selectbox(
        "Select Page:",
        list(page_options.keys()),
        format_func=lambda x: x
    )

    page = page_options[selected_page]

    # Add some sidebar info
    st.sidebar.markdown("---")
    st.sidebar.markdown("""
    <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px; color: white;">
        <h4>💡 Quick Tips</h4>
        <ul style="font-size: 0.9rem;">
            <li>Start with Data Entry to add sales</li>
            <li>View Reports for summaries</li>
            <li>Analyze trends in Statistics</li>
            <li>Download data as CSV</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

    # Page routing with loading animation
    if page == "Data Entry":
        data_entry_page()
    elif page == "Sales Report":
        sales_report_page()
    else:
        statistical_analysis_page()

def data_entry_page():
    # Page header with animation
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                border-radius: 15px; margin-bottom: 2rem; animation: fadeIn 1s ease;">
        <h2 style="color: white; margin: 0; font-size: 2.5rem;">📝 Sales Data Entry</h2>
        <p style="color: white; opacity: 0.9; margin: 0.5rem 0 0 0;">Add new sales transactions to your database</p>
    </div>
    """, unsafe_allow_html=True)

    # Create two columns for better layout
    col1, col2 = st.columns([1.2, 0.8], gap="large")

    with col1:
        # Input form container
        st.markdown("""
        <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        """, unsafe_allow_html=True)

        st.markdown("### 🛍️ Enter Sale Details")

        # Input fields with enhanced styling
        sale_date = st.date_input("📅 Sale Date", value=date.today(), help="Select the date of sale")

        product_name = st.text_input(
            "🏷️ Product Name",
            placeholder="Enter product name (e.g., iPhone 15, Milk, T-Shirt)",
            help="Enter the name of the product being sold"
        )

        # Enhanced category selection with emojis
        categories = {
            "🥬 Groceries": "Groceries",
            "📱 Electronics": "Electronics",
            "👕 Clothing": "Clothing",
            "🏡 Home & Garden": "Home & Garden",
            "⚽ Sports": "Sports",
            "📚 Books": "Books",
            "🔧 Other": "Other"
        }

        selected_category = st.selectbox(
            "📂 Category",
            list(categories.keys()),
            help="Select the product category"
        )
        category = categories[selected_category]

        # Quantity and price inputs
        col_qty, col_price = st.columns(2)
        with col_qty:
            quantity = st.number_input(
                "📦 Quantity",
                min_value=1,
                value=1,
                step=1,
                help="Number of items sold"
            )

        with col_price:
            unit_price = st.number_input(
                "💰 Unit Price ($)",
                min_value=0.01,
                value=1.00,
                step=0.01,
                format="%.2f",
                help="Price per unit"
            )

        # Calculate total with enhanced display
        total_amount = quantity * unit_price
        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #4ECDC4, #44A08D); padding: 1rem;
                    border-radius: 10px; text-align: center; margin: 1rem 0;">
            <h3 style="color: white; margin: 0;">💵 Total Amount: ${total_amount:.2f}</h3>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced add entry button
        if st.button("✨ Add Sale Entry", type="primary", use_container_width=True):
            if product_name.strip():
                add_sale_entry(sale_date, product_name, category, quantity, unit_price)
                st.success(f"🎉 Sale entry for '{product_name}' added successfully!")
                st.balloons()
                time.sleep(0.5)  # Brief pause for better UX
            else:
                st.error("⚠️ Please enter a product name!")

        st.markdown("</div>", unsafe_allow_html=True)

    with col2:
        # Recent entries container
        st.markdown("""
        <div style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        """, unsafe_allow_html=True)

        st.markdown("### 📋 Recent Entries")

        if not st.session_state.sales_data.empty:
            # Show last 5 entries with better formatting
            recent_data = st.session_state.sales_data.tail(5).copy()
            recent_data['Date'] = pd.to_datetime(recent_data['Date']).dt.strftime('%Y-%m-%d')
            recent_data['Unit_Price'] = recent_data['Unit_Price'].apply(lambda x: f"${x:.2f}")
            recent_data['Total_Amount'] = recent_data['Total_Amount'].apply(lambda x: f"${x:.2f}")

            # Display with custom styling
            st.dataframe(
                recent_data,
                use_container_width=True,
                hide_index=True
            )

            # Show total entries count
            total_entries = len(st.session_state.sales_data)
            st.markdown(f"""
            <div style="text-align: center; padding: 0.5rem; background: #f0f2f6; border-radius: 5px; margin-top: 1rem;">
                <small>📊 Total Entries: <strong>{total_entries}</strong></small>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div style="text-align: center; padding: 2rem; background: #f8f9fa; border-radius: 10px; border: 2px dashed #dee2e6;">
                <h4 style="color: #6c757d;">📝 No entries yet</h4>
                <p style="color: #6c757d; margin: 0;">Add your first sale to get started!</p>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("</div>", unsafe_allow_html=True)

    # Enhanced clear data section
    st.markdown("---")
    st.markdown("### 🗑️ Data Management")

    col_clear, col_info = st.columns([1, 2])
    with col_clear:
        if st.button("🗑️ Clear All Data", type="secondary", use_container_width=True):
            if st.session_state.sales_data.empty:
                st.warning("⚠️ No data to clear!")
            else:
                st.session_state.sales_data = pd.DataFrame(columns=[
                    'Date', 'Product_Name', 'Category', 'Quantity', 'Unit_Price', 'Total_Amount'
                ])
                st.success("🧹 All data cleared successfully!")

    with col_info:
        st.info("💡 **Tip:** Use the Clear All Data button to reset your sales database. This action cannot be undone!")

def sales_report_page():
    # Enhanced page header
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(45deg, #667eea, #764ba2);
                border-radius: 15px; margin-bottom: 2rem;">
        <h2 style="color: white; margin: 0; font-size: 2.5rem;">📊 Sales Report Dashboard</h2>
        <p style="color: white; opacity: 0.9; margin: 0.5rem 0 0 0;">Comprehensive overview of your sales performance</p>
    </div>
    """, unsafe_allow_html=True)

    if st.session_state.sales_data.empty:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: white; border-radius: 15px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3 style="color: #6c757d;">📭 No Sales Data Available</h3>
            <p style="color: #6c757d;">Please add some sales entries first to view reports!</p>
            <div style="margin-top: 2rem;">
                <a href="#" style="background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white;
                   padding: 0.8rem 2rem; border-radius: 25px; text-decoration: none; font-weight: bold;">
                   ➕ Add Sales Data
                </a>
            </div>
        </div>
        """, unsafe_allow_html=True)
        return

    # Calculate metrics
    total_sales = st.session_state.sales_data['Total_Amount'].sum()
    total_items = st.session_state.sales_data['Quantity'].sum()
    avg_sale = st.session_state.sales_data['Total_Amount'].mean()
    num_transactions = len(st.session_state.sales_data)

    # Enhanced metrics display with cards
    st.markdown("### 📈 Key Performance Indicators")

    col1, col2, col3, col4 = st.columns(4)

    metrics = [
        ("💰 Total Sales", f"${total_sales:.2f}", "#FF6B6B", "📈"),
        ("📦 Items Sold", f"{total_items:,}", "#4ECDC4", "📊"),
        ("💵 Average Sale", f"${avg_sale:.2f}", "#45B7D1", "📋"),
        ("🛒 Transactions", f"{num_transactions:,}", "#96CEB4", "🔢")
    ]

    for col, (title, value, color, icon) in zip([col1, col2, col3, col4], metrics):
        with col:
            st.markdown(f"""
            <div style="background: white; padding: 1.5rem; border-radius: 15px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1); text-align: center;
                        border-left: 5px solid {color}; transition: transform 0.3s ease;">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">{icon}</div>
                <h4 style="color: #333; margin: 0; font-size: 0.9rem;">{title}</h4>
                <h2 style="color: {color}; margin: 0.5rem 0 0 0; font-size: 1.8rem;">{value}</h2>
            </div>
            """, unsafe_allow_html=True)

    st.markdown("---")

    # Enhanced data table section
    st.markdown("### 📋 Complete Sales Database")

    # Add filters
    col_filter1, col_filter2, col_filter3 = st.columns(3)

    with col_filter1:
        # Category filter
        categories = ['All'] + list(st.session_state.sales_data['Category'].unique())
        selected_category = st.selectbox("🏷️ Filter by Category", categories)

    with col_filter2:
        # Date range filter
        min_date = st.session_state.sales_data['Date'].min()
        max_date = st.session_state.sales_data['Date'].max()
        date_range = st.date_input(
            "� Date Range",
            value=(min_date, max_date),
            min_value=min_date,
            max_value=max_date
        )

    with col_filter3:
        # Sort options
        sort_options = {
            "Date (Newest First)": ("Date", False),
            "Date (Oldest First)": ("Date", True),
            "Amount (Highest First)": ("Total_Amount", False),
            "Amount (Lowest First)": ("Total_Amount", True)
        }
        sort_selection = st.selectbox("🔄 Sort by", list(sort_options.keys()))

    # Apply filters
    filtered_data = st.session_state.sales_data.copy()

    if selected_category != 'All':
        filtered_data = filtered_data[filtered_data['Category'] == selected_category]

    if len(date_range) == 2:
        start_date, end_date = date_range
        filtered_data = filtered_data[
            (pd.to_datetime(filtered_data['Date']).dt.date >= start_date) &
            (pd.to_datetime(filtered_data['Date']).dt.date <= end_date)
        ]

    # Apply sorting
    sort_col, ascending = sort_options[sort_selection]
    filtered_data = filtered_data.sort_values(sort_col, ascending=ascending)

    # Format the data for display
    display_data = filtered_data.copy()
    display_data['Date'] = pd.to_datetime(display_data['Date']).dt.strftime('%Y-%m-%d')
    display_data['Unit_Price'] = display_data['Unit_Price'].apply(lambda x: f"${x:.2f}")
    display_data['Total_Amount'] = display_data['Total_Amount'].apply(lambda x: f"${x:.2f}")

    # Display filtered results count
    st.markdown(f"""
    <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px; margin: 1rem 0;">
        <strong>📊 Showing {len(display_data)} of {len(st.session_state.sales_data)} total records</strong>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced data table
    st.dataframe(
        display_data,
        use_container_width=True,
        hide_index=True
    )

    # Enhanced download section
    st.markdown("---")
    st.markdown("### 📥 Export Data")

    col_download, col_info = st.columns([1, 2])

    with col_download:
        csv = filtered_data.to_csv(index=False)
        st.download_button(
            label="📥 Download Filtered Data as CSV",
            data=csv,
            file_name=f"sales_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
            use_container_width=True
        )

    with col_info:
        st.info("💡 **Export Info:** Download includes all filtered data with original formatting for further analysis in Excel or other tools.")

def statistical_analysis_page():
    # Enhanced page header
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(45deg, #96CEB4, #FFECD2);
                border-radius: 15px; margin-bottom: 2rem;">
        <h2 style="color: #333; margin: 0; font-size: 2.5rem;">📈 Statistical Analysis Center</h2>
        <p style="color: #555; opacity: 0.9; margin: 0.5rem 0 0 0;">Deep insights and data visualizations for informed decisions</p>
    </div>
    """, unsafe_allow_html=True)

    if st.session_state.sales_data.empty:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: white; border-radius: 15px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3 style="color: #6c757d;">📊 No Data for Analysis</h3>
            <p style="color: #6c757d;">Add sales data to unlock powerful analytics and insights!</p>
        </div>
        """, unsafe_allow_html=True)
        return

    data = st.session_state.sales_data.copy()
    data['Date'] = pd.to_datetime(data['Date'])

    # Enhanced Summary Statistics with cards
    st.markdown("### 📊 Statistical Summary")

    col1, col2 = st.columns(2, gap="large")

    with col1:
        st.markdown("""
        <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                    border-left: 5px solid #FF6B6B;">
            <h4 style="color: #333; margin: 0 0 1rem 0;">💰 Sales Amount Statistics</h4>
        </div>
        """, unsafe_allow_html=True)

        stats_data = [
            ("📊 Mean", f"${data['Total_Amount'].mean():.2f}"),
            ("📍 Median", f"${data['Total_Amount'].median():.2f}"),
            ("📈 Std Deviation", f"${data['Total_Amount'].std():.2f}"),
            ("📉 Minimum", f"${data['Total_Amount'].min():.2f}"),
            ("📊 Maximum", f"${data['Total_Amount'].max():.2f}")
        ]

        for metric, value in stats_data:
            st.markdown(f"""
            <div style="background: #f8f9fa; padding: 1rem; margin: 0.5rem 0; border-radius: 8px;
                        display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold; color: #333;">{metric}</span>
                <span style="color: #FF6B6B; font-weight: bold; font-size: 1.1rem;">{value}</span>
            </div>
            """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                    border-left: 5px solid #4ECDC4;">
            <h4 style="color: #333; margin: 0 0 1rem 0;">📦 Quantity Statistics</h4>
        </div>
        """, unsafe_allow_html=True)

        qty_stats_data = [
            ("📊 Mean", f"{data['Quantity'].mean():.1f}"),
            ("📍 Median", f"{data['Quantity'].median():.1f}"),
            ("📈 Std Deviation", f"{data['Quantity'].std():.1f}"),
            ("📉 Minimum", f"{data['Quantity'].min()}"),
            ("📊 Maximum", f"{data['Quantity'].max()}")
        ]

        for metric, value in qty_stats_data:
            st.markdown(f"""
            <div style="background: #f8f9fa; padding: 1rem; margin: 0.5rem 0; border-radius: 8px;
                        display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold; color: #333;">{metric}</span>
                <span style="color: #4ECDC4; font-weight: bold; font-size: 1.1rem;">{value}</span>
            </div>
            """, unsafe_allow_html=True)

    st.markdown("---")

    # Enhanced Charts and Visualizations
    st.markdown("### 📈 Interactive Data Visualizations")

    # Sales by Category with enhanced styling
    col1, col2 = st.columns(2, gap="large")

    with col1:
        st.markdown("""
        <div class="chart-container">
            <h4 style="text-align: center; color: #333; margin-bottom: 1rem;">🥧 Sales Distribution by Category</h4>
        </div>
        """, unsafe_allow_html=True)

        category_sales = data.groupby('Category')['Total_Amount'].sum().reset_index()
        fig_pie = px.pie(
            category_sales,
            values='Total_Amount',
            names='Category',
            color_discrete_sequence=px.colors.qualitative.Set3,
            hole=0.4
        )
        fig_pie.update_traces(textposition='inside', textinfo='percent+label')
        fig_pie.update_layout(
            showlegend=True,
            height=400,
            font=dict(size=12),
            margin=dict(t=20, b=20, l=20, r=20)
        )
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        st.markdown("""
        <div class="chart-container">
            <h4 style="text-align: center; color: #333; margin-bottom: 1rem;">📊 Quantity Sold by Category</h4>
        </div>
        """, unsafe_allow_html=True)

        category_qty = data.groupby('Category')['Quantity'].sum().reset_index()
        fig_bar = px.bar(
            category_qty,
            x='Category',
            y='Quantity',
            color='Quantity',
            color_continuous_scale='Viridis'
        )
        fig_bar.update_layout(
            xaxis_tickangle=-45,
            height=400,
            showlegend=False,
            margin=dict(t=20, b=60, l=20, r=20)
        )
        fig_bar.update_traces(texttemplate='%{y}', textposition='outside')
        st.plotly_chart(fig_bar, use_container_width=True)

    # Time series analysis (if we have multiple dates)
    if len(data['Date'].dt.date.unique()) > 1:
        st.markdown("""
        <div class="chart-container">
            <h4 style="text-align: center; color: #333; margin-bottom: 1rem;">📈 Sales Trend Over Time</h4>
        </div>
        """, unsafe_allow_html=True)

        daily_sales = data.groupby(data['Date'].dt.date)['Total_Amount'].sum().reset_index()
        daily_sales.columns = ['Date', 'Total_Sales']

        fig_line = px.line(
            daily_sales,
            x='Date',
            y='Total_Sales',
            markers=True,
            line_shape='spline'
        )
        fig_line.update_traces(
            line=dict(width=3, color='#FF6B6B'),
            marker=dict(size=8, color='#4ECDC4')
        )
        fig_line.update_layout(
            height=400,
            hovermode='x unified',
            margin=dict(t=20, b=20, l=20, r=20)
        )
        st.plotly_chart(fig_line, use_container_width=True)

    # Top products with enhanced styling
    st.markdown("""
    <div class="chart-container">
        <h4 style="text-align: center; color: #333; margin-bottom: 1rem;">🏆 Top 10 Products by Sales</h4>
    </div>
    """, unsafe_allow_html=True)

    top_products = data.groupby('Product_Name')['Total_Amount'].sum().sort_values(ascending=False).head(10)
    if not top_products.empty:
        fig_top = px.bar(
            x=top_products.values,
            y=top_products.index,
            orientation='h',
            color=top_products.values,
            color_continuous_scale='RdYlBu_r'
        )
        fig_top.update_layout(
            yaxis={'categoryorder':'total ascending'},
            height=500,
            showlegend=False,
            margin=dict(t=20, b=20, l=20, r=20)
        )
        fig_top.update_traces(texttemplate='$%{x:.2f}', textposition='outside')
        st.plotly_chart(fig_top, use_container_width=True)

    # Price distribution with enhanced styling
    st.markdown("""
    <div class="chart-container">
        <h4 style="text-align: center; color: #333; margin-bottom: 1rem;">💰 Unit Price Distribution</h4>
    </div>
    """, unsafe_allow_html=True)

    fig_hist = px.histogram(
        data,
        x='Unit_Price',
        nbins=20,
        color_discrete_sequence=['#45B7D1']
    )
    fig_hist.update_layout(
        height=400,
        showlegend=False,
        margin=dict(t=20, b=20, l=20, r=20)
    )
    fig_hist.update_traces(opacity=0.7)
    st.plotly_chart(fig_hist, use_container_width=True)

    # Additional insights section
    st.markdown("---")
    st.markdown("### 🔍 Key Insights")

    # Calculate insights
    most_popular_category = data.groupby('Category')['Quantity'].sum().idxmax()
    highest_revenue_category = data.groupby('Category')['Total_Amount'].sum().idxmax()
    best_selling_product = data.groupby('Product_Name')['Quantity'].sum().idxmax()
    most_expensive_sale = data.loc[data['Total_Amount'].idxmax()]

    insight_col1, insight_col2 = st.columns(2)

    with insight_col1:
        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #FF6B6B, #FF8E53); padding: 1.5rem;
                    border-radius: 15px; color: white; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0;">🎯 Most Popular Category</h4>
            <p style="margin: 0; font-size: 1.2rem; font-weight: bold;">{most_popular_category}</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #4ECDC4, #44A08D); padding: 1.5rem;
                    border-radius: 15px; color: white; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0;">💎 Best Selling Product</h4>
            <p style="margin: 0; font-size: 1.2rem; font-weight: bold;">{best_selling_product}</p>
        </div>
        """, unsafe_allow_html=True)

    with insight_col2:
        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2); padding: 1.5rem;
                    border-radius: 15px; color: white; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0;">💰 Highest Revenue Category</h4>
            <p style="margin: 0; font-size: 1.2rem; font-weight: bold;">{highest_revenue_category}</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #96CEB4, #FFECD2); padding: 1.5rem;
                    border-radius: 15px; color: #333; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0;">🏆 Largest Single Sale</h4>
            <p style="margin: 0; font-size: 1.2rem; font-weight: bold;">${most_expensive_sale['Total_Amount']:.2f}</p>
            <small style="opacity: 0.8;">{most_expensive_sale['Product_Name']}</small>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()

if __name__ == "__main__":
    main()