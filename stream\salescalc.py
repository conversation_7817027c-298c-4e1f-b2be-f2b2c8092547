import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date
import numpy as np

# Page configuration
st.set_page_config(
    page_title="Supermarket Sales Calculator",
    page_icon="🛒",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state for storing sales data
if 'sales_data' not in st.session_state:
    st.session_state.sales_data = pd.DataFrame(columns=[
        'Date', 'Product_Name', 'Category', 'Quantity', 'Unit_Price', 'Total_Amount'
    ])

def add_sale_entry(date, product_name, category, quantity, unit_price):
    """Add a new sale entry to the session state"""
    total_amount = quantity * unit_price
    new_entry = pd.DataFrame({
        'Date': [date],
        'Product_Name': [product_name],
        'Category': [category],
        'Quantity': [quantity],
        'Unit_Price': [unit_price],
        'Total_Amount': [total_amount]
    })
    st.session_state.sales_data = pd.concat([st.session_state.sales_data, new_entry], ignore_index=True)

def main():
    st.title("🛒 Supermarket Sales Calculator")
    st.markdown("---")

    # Sidebar for navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox("Choose a page:", ["Data Entry", "Sales Report", "Statistical Analysis"])

    if page == "Data Entry":
        data_entry_page()
    elif page == "Sales Report":
        sales_report_page()
    else:
        statistical_analysis_page()

def data_entry_page():
    st.header("📝 Sales Data Entry")

    # Create two columns for better layout
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Enter Sale Details")

        # Input fields
        sale_date = st.date_input("Sale Date", value=date.today())
        product_name = st.text_input("Product Name", placeholder="Enter product name")

        # Category selection
        categories = ["Groceries", "Electronics", "Clothing", "Home & Garden", "Sports", "Books", "Other"]
        category = st.selectbox("Category", categories)

        quantity = st.number_input("Quantity", min_value=1, value=1, step=1)
        unit_price = st.number_input("Unit Price ($)", min_value=0.01, value=1.00, step=0.01, format="%.2f")

        # Calculate total automatically
        total_amount = quantity * unit_price
        st.info(f"Total Amount: ${total_amount:.2f}")

        # Add entry button
        if st.button("Add Sale Entry", type="primary"):
            if product_name.strip():
                add_sale_entry(sale_date, product_name, category, quantity, unit_price)
                st.success(f"✅ Sale entry added successfully!")
                st.balloons()
            else:
                st.error("Please enter a product name!")

    with col2:
        st.subheader("Recent Entries")
        if not st.session_state.sales_data.empty:
            # Show last 5 entries
            recent_data = st.session_state.sales_data.tail(5).copy()
            recent_data['Date'] = pd.to_datetime(recent_data['Date']).dt.strftime('%Y-%m-%d')
            recent_data['Unit_Price'] = recent_data['Unit_Price'].apply(lambda x: f"${x:.2f}")
            recent_data['Total_Amount'] = recent_data['Total_Amount'].apply(lambda x: f"${x:.2f}")
            st.dataframe(recent_data, use_container_width=True)
        else:
            st.info("No sales entries yet. Add your first sale above!")

    # Clear all data button
    st.markdown("---")
    if st.button("🗑️ Clear All Data", type="secondary"):
        if st.session_state.sales_data.empty:
            st.warning("No data to clear!")
        else:
            st.session_state.sales_data = pd.DataFrame(columns=[
                'Date', 'Product_Name', 'Category', 'Quantity', 'Unit_Price', 'Total_Amount'
            ])
            st.success("All data cleared!")

def sales_report_page():
    st.header("📊 Sales Report")

    if st.session_state.sales_data.empty:
        st.warning("No sales data available. Please add some entries first!")
        return

    # Display summary metrics
    col1, col2, col3, col4 = st.columns(4)

    total_sales = st.session_state.sales_data['Total_Amount'].sum()
    total_items = st.session_state.sales_data['Quantity'].sum()
    avg_sale = st.session_state.sales_data['Total_Amount'].mean()
    num_transactions = len(st.session_state.sales_data)

    with col1:
        st.metric("Total Sales", f"${total_sales:.2f}")
    with col2:
        st.metric("Total Items Sold", f"{total_items:,}")
    with col3:
        st.metric("Average Sale", f"${avg_sale:.2f}")
    with col4:
        st.metric("Total Transactions", f"{num_transactions:,}")

    st.markdown("---")

    # Display full data table
    st.subheader("📋 Complete Sales Data")

    # Format the data for display
    display_data = st.session_state.sales_data.copy()
    display_data['Date'] = pd.to_datetime(display_data['Date']).dt.strftime('%Y-%m-%d')
    display_data['Unit_Price'] = display_data['Unit_Price'].apply(lambda x: f"${x:.2f}")
    display_data['Total_Amount'] = display_data['Total_Amount'].apply(lambda x: f"${x:.2f}")

    st.dataframe(display_data, use_container_width=True)

    # Download button for CSV
    csv = st.session_state.sales_data.to_csv(index=False)
    st.download_button(
        label="📥 Download Sales Data as CSV",
        data=csv,
        file_name=f"sales_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv"
    )

def statistical_analysis_page():
    st.header("📈 Statistical Analysis")

    if st.session_state.sales_data.empty:
        st.warning("No sales data available. Please add some entries first!")
        return

    data = st.session_state.sales_data.copy()
    data['Date'] = pd.to_datetime(data['Date'])

    # Summary Statistics
    st.subheader("📊 Summary Statistics")
    col1, col2 = st.columns(2)

    with col1:
        st.write("**Sales Amount Statistics:**")
        stats_df = pd.DataFrame({
            'Metric': ['Mean', 'Median', 'Standard Deviation', 'Minimum', 'Maximum'],
            'Value': [
                f"${data['Total_Amount'].mean():.2f}",
                f"${data['Total_Amount'].median():.2f}",
                f"${data['Total_Amount'].std():.2f}",
                f"${data['Total_Amount'].min():.2f}",
                f"${data['Total_Amount'].max():.2f}"
            ]
        })
        st.dataframe(stats_df, use_container_width=True, hide_index=True)

    with col2:
        st.write("**Quantity Statistics:**")
        qty_stats_df = pd.DataFrame({
            'Metric': ['Mean', 'Median', 'Standard Deviation', 'Minimum', 'Maximum'],
            'Value': [
                f"{data['Quantity'].mean():.1f}",
                f"{data['Quantity'].median():.1f}",
                f"{data['Quantity'].std():.1f}",
                f"{data['Quantity'].min()}",
                f"{data['Quantity'].max()}"
            ]
        })
        st.dataframe(qty_stats_df, use_container_width=True, hide_index=True)

    st.markdown("---")

    # Charts and Visualizations
    st.subheader("📈 Data Visualizations")

    # Sales by Category
    col1, col2 = st.columns(2)

    with col1:
        st.write("**Sales by Category**")
        category_sales = data.groupby('Category')['Total_Amount'].sum().reset_index()
        fig_pie = px.pie(category_sales, values='Total_Amount', names='Category',
                        title="Sales Distribution by Category")
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        st.write("**Quantity Sold by Category**")
        category_qty = data.groupby('Category')['Quantity'].sum().reset_index()
        fig_bar = px.bar(category_qty, x='Category', y='Quantity',
                        title="Quantity Sold by Category")
        fig_bar.update_xaxis(tickangle=45)
        st.plotly_chart(fig_bar, use_container_width=True)

    # Time series analysis (if we have multiple dates)
    if len(data['Date'].dt.date.unique()) > 1:
        st.write("**Sales Trend Over Time**")
        daily_sales = data.groupby(data['Date'].dt.date)['Total_Amount'].sum().reset_index()
        daily_sales.columns = ['Date', 'Total_Sales']

        fig_line = px.line(daily_sales, x='Date', y='Total_Sales',
                          title="Daily Sales Trend", markers=True)
        st.plotly_chart(fig_line, use_container_width=True)

    # Top products
    st.write("**Top 10 Products by Sales**")
    top_products = data.groupby('Product_Name')['Total_Amount'].sum().sort_values(ascending=False).head(10)
    if not top_products.empty:
        fig_top = px.bar(x=top_products.values, y=top_products.index, orientation='h',
                        title="Top 10 Products by Sales Amount")
        fig_top.update_layout(yaxis={'categoryorder':'total ascending'})
        st.plotly_chart(fig_top, use_container_width=True)

    # Price distribution
    st.write("**Unit Price Distribution**")
    fig_hist = px.histogram(data, x='Unit_Price', nbins=20,
                           title="Distribution of Unit Prices")
    st.plotly_chart(fig_hist, use_container_width=True)

if __name__ == "__main__":
    main()