import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import random

class TransactionWindow:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Transaction Manager")
        self.window.geometry("900x700")
        self.window.configure(bg='#f0f0f0')
        
        self.transactions = self.generate_sample_transactions()
        self.create_interface()
        
    def generate_sample_transactions(self):
        transactions = []
        transaction_types = ['Deposit', 'Withdrawal', 'Transfer', 'Payment']
        descriptions = ['ATM Withdrawal', 'Direct Deposit', 'Online Transfer', 'Bill Payment', 'Purchase']
        
        for i in range(20):
            date = datetime.now() - timedelta(days=random.randint(0, 30))
            transactions.append({
                'id': f'TXN{i+1:03d}',
                'date': date.strftime('%Y-%m-%d'),
                'type': random.choice(transaction_types),
                'description': random.choice(descriptions),
                'amount': round(random.uniform(-500, 1000), 2),
                'account': random.choice(['Checking', 'Savings', 'Credit'])
            })
        
        return sorted(transactions, key=lambda x: x['date'], reverse=True)
        
    def create_interface(self):
        # Title
        title_label = tk.Label(self.window, text="Transaction History", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=20)
        
        # Filter frame
        filter_frame = tk.Frame(self.window, bg='#f0f0f0')
        filter_frame.pack(pady=10)
        
        tk.Label(filter_frame, text="Filter by Account:", bg='#f0f0f0').pack(side='left', padx=5)
        self.account_filter = ttk.Combobox(filter_frame, values=['All', 'Checking', 'Savings', 'Credit'])
        self.account_filter.set('All')
        self.account_filter.pack(side='left', padx=5)
        
        tk.Button(filter_frame, text="Apply Filter", command=self.apply_filter,
                 bg='#3498db', fg='white').pack(side='left', padx=10)
        
        # Action buttons
        button_frame = tk.Frame(self.window, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="New Transaction", command=self.new_transaction,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Edit Transaction", command=self.edit_transaction,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Delete Transaction", command=self.delete_transaction,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        # Transaction list
        self.create_transaction_list()
        
        # Summary frame
        self.create_summary_frame()
        
    def create_transaction_list(self):
        # Treeview for transaction list
        columns = ('ID', 'Date', 'Type', 'Description', 'Amount', 'Account')
        self.tree = ttk.Treeview(self.window, columns=columns, show='headings', height=15)
        
        # Define headings
        column_widths = {'ID': 80, 'Date': 100, 'Type': 100, 'Description': 200, 'Amount': 100, 'Account': 100}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[col])
        
        # Insert data
        self.populate_tree()
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(self.window, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=20)
        scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=20)
        
    def populate_tree(self, transactions=None):
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Insert transactions
        transactions = transactions or self.transactions
        for txn in transactions:
            self.tree.insert('', tk.END, values=(
                txn['id'], txn['date'], txn['type'], 
                txn['description'], f"${txn['amount']:,.2f}", txn['account']
            ))
    
    def create_summary_frame(self):
        summary_frame = tk.LabelFrame(self.window, text="Transaction Summary", 
                                     font=('Arial', 12, 'bold'), bg='#f0f0f0')
        summary_frame.pack(fill='x', padx=20, pady=10)
        
        total_in = sum(txn['amount'] for txn in self.transactions if txn['amount'] > 0)
        total_out = sum(txn['amount'] for txn in self.transactions if txn['amount'] < 0)
        net_change = total_in + total_out
        
        tk.Label(summary_frame, text=f"Total Income: ${total_in:,.2f}", 
                fg='green', bg='#f0f0f0', font=('Arial', 10, 'bold')).pack(side='left', padx=20)
        tk.Label(summary_frame, text=f"Total Expenses: ${abs(total_out):,.2f}", 
                fg='red', bg='#f0f0f0', font=('Arial', 10, 'bold')).pack(side='left', padx=20)
        tk.Label(summary_frame, text=f"Net Change: ${net_change:,.2f}", 
                fg='blue', bg='#f0f0f0', font=('Arial', 10, 'bold')).pack(side='left', padx=20)
    
    def apply_filter(self):
        filter_account = self.account_filter.get()
        if filter_account == 'All':
            filtered_transactions = self.transactions
        else:
            filtered_transactions = [txn for txn in self.transactions if txn['account'] == filter_account]
        
        self.populate_tree(filtered_transactions)
    
    def new_transaction(self):
        messagebox.showinfo("New Transaction", "New transaction dialog coming soon!")
        
    def edit_transaction(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select a transaction to edit")
            return
        messagebox.showinfo("Edit Transaction", "Edit transaction dialog coming soon!")
        
    def delete_transaction(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select a transaction to delete")
            return
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this transaction?"):
            self.tree.delete(selected[0])
    
    def transaction_dialog(self, title, item=None):
        dialog = tk.Toplevel(self.window)
        dialog.title(title)
        dialog.geometry("400x400")
        dialog.configure(bg='#f0f0f0')
        
        # Form fields
        fields = [
            ("Transaction Type:", ['Deposit', 'Withdrawal', 'Transfer', 'Payment']),
            ("Account:", ['Checking', 'Savings', 'Credit']),
            ("Description:", None),
            ("Amount:", None),
            ("Date:", None)
        ]
        
        vars_dict = {}
        
        for label_text, values in fields:
            tk.Label(dialog, text=label_text, bg='#f0f0f0').pack(pady=5)
            
            if values:  # Combobox
                var = tk.StringVar()
                combo = ttk.Combobox(dialog, textvariable=var, values=values)
                combo.pack(pady=5)
                vars_dict[label_text] = var
            else:  # Entry
                var = tk.StringVar()
                if label_text == "Date:":
                    var.set(datetime.now().strftime('%Y-%m-%d'))
                entry = tk.Entry(dialog, textvariable=var, width=30)
                entry.pack(pady=5)
                vars_dict[label_text] = var
        
        # Buttons
        button_frame = tk.Frame(dialog, bg='#f0f0f0')
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="Save", 
                 command=lambda: self.save_transaction(dialog, vars_dict, item),
                 bg='#27ae60', fg='white').pack(side='left', padx=10)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy,
                 bg='#95a5a6', fg='white').pack(side='left', padx=10)
    
    def save_transaction(self, dialog, vars_dict, item=None):
        try:
            txn_type = vars_dict["Transaction Type:"].get()
            account = vars_dict["Account:"].get()
            description = vars_dict["Description:"].get()
            amount = float(vars_dict["Amount:"].get())
            date = vars_dict["Date:"].get()
            
            if not all([txn_type, account, description, date]):
                messagebox.showerror("Error", "Please fill all fields")
                return
            
            if item:  # Edit existing
                self.tree.item(item, values=(
                    self.tree.item(item)['values'][0],  # Keep same ID
                    date, txn_type, description, f"${amount:,.2f}", account
                ))
            else:  # Add new
                new_id = f"TXN{len(self.transactions) + 1:03d}"
                self.tree.insert('', tk.END, values=(new_id, date, txn_type, description, f"${amount:,.2f}", account))
            
            dialog.destroy()
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid amount")
