# 🛒 SuperMart Analytics Dashboard

A stunning, feature-rich Streamlit application for managing and analyzing supermarket sales data with beautiful visualizations and enhanced user experience.

## Features

### 1. Data Entry Page
- **User-friendly form** to enter sales data
- **Real-time calculation** of total amounts
- **Product categorization** (Groceries, Electronics, Clothing, etc.)
- **Recent entries display** showing last 5 transactions
- **Data validation** to ensure required fields are filled
- **Clear all data** functionality

### 2. Sales Report Page
- **Summary metrics** displaying:
  - Total Sales Amount
  - Total Items Sold
  - Average Sale Amount
  - Total Number of Transactions
- **Complete data table** with formatted currency display
- **CSV download** functionality for data export
- **Professional formatting** with proper date and currency display

### 3. Statistical Analysis Page
- **Summary Statistics** for both sales amounts and quantities:
  - Mean, Median, Standard Deviation
  - Minimum and Maximum values
- **Interactive Visualizations**:
  - Pie chart showing sales distribution by category
  - Bar chart showing quantity sold by category
  - Time series line chart for sales trends (when multiple dates exist)
  - Top 10 products by sales amount
  - Unit price distribution histogram

## Installation

1. Install the required packages:
```bash
pip install streamlit pandas plotly
```

Or install from requirements.txt:
```bash
pip install -r requirements.txt
```

## Usage

1. Navigate to the stream directory:
```bash
cd stream
```

2. Run the Streamlit application:
```bash
streamlit run salescalc.py
```

3. The application will open in your default web browser at `http://localhost:8501`

## How to Use

### Adding Sales Data
1. Go to the **Data Entry** page
2. Fill in the sale details:
   - Sale Date
   - Product Name
   - Category
   - Quantity
   - Unit Price
3. Click **Add Sale Entry** to save the data
4. View recent entries in the right column

### Viewing Reports
1. Go to the **Sales Report** page
2. View summary metrics at the top
3. Browse the complete data table
4. Download data as CSV if needed

### Analyzing Data
1. Go to the **Statistical Analysis** page
2. Review summary statistics
3. Explore interactive charts and visualizations
4. Analyze trends and patterns in your sales data

## Data Persistence
- Data is stored in the browser session
- Data persists while the app is running
- Use the CSV download feature to save data permanently
- Clear all data using the "Clear All Data" button when needed

## Technologies Used
- **Streamlit**: Web application framework
- **Pandas**: Data manipulation and analysis
- **Plotly Express**: Interactive data visualizations
- **Python**: Core programming language

## File Structure
```
stream/
├── salescalc.py          # Main application file
├── README_salescalc.md   # This documentation
└── requirements.txt      # Python dependencies
```
