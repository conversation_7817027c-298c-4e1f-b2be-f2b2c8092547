"""
Demo script to add sample data to the SuperMart Analytics Dashboard
Run this after starting the Streamlit app to populate it with sample data
"""

import pandas as pd
from datetime import datetime, date, timedelta
import random

def generate_sample_data():
    """Generate sample sales data for demonstration"""
    
    # Sample products with categories
    products = {
        "Groceries": ["Milk 1L", "Bread Loaf", "Eggs 12pk", "Bananas 1kg", "Rice 5kg", "Chicken 1kg", "Tomatoes 1kg"],
        "Electronics": ["iPhone 15", "Samsung TV 55\"", "Laptop Dell", "Wireless Headphones", "Smart Watch", "Tablet iPad"],
        "Clothing": ["T-Shirt Cotton", "Jeans Blue", "Sneakers Nike", "Jacket Winter", "Dress Summer", "Socks 3pk"],
        "Home & Garden": ["Plant Pot", "Garden Hose", "Lawn Mower", "Fertilizer", "Seeds Packet", "Watering Can"],
        "Sports": ["Football", "Tennis Racket", "Yoga Mat", "Dumbbells 5kg", "Basketball", "Swimming Goggles"],
        "Books": ["Python Programming", "Data Science Guide", "Fiction Novel", "Cookbook", "History Book", "Magazine"]
    }
    
    # Price ranges for different categories
    price_ranges = {
        "Groceries": (1.50, 25.00),
        "Electronics": (50.00, 1200.00),
        "Clothing": (15.00, 150.00),
        "Home & Garden": (5.00, 200.00),
        "Sports": (10.00, 300.00),
        "Books": (8.00, 45.00)
    }
    
    sample_data = []
    
    # Generate data for the last 30 days
    for i in range(100):  # 100 sample transactions
        # Random date in the last 30 days
        days_ago = random.randint(0, 29)
        sale_date = date.today() - timedelta(days=days_ago)
        
        # Random category and product
        category = random.choice(list(products.keys()))
        product_name = random.choice(products[category])
        
        # Random quantity (weighted towards smaller quantities)
        quantity = random.choices([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 
                                weights=[30, 25, 15, 10, 8, 5, 3, 2, 1, 1])[0]
        
        # Random price within category range
        min_price, max_price = price_ranges[category]
        unit_price = round(random.uniform(min_price, max_price), 2)
        
        total_amount = quantity * unit_price
        
        sample_data.append({
            'Date': sale_date,
            'Product_Name': product_name,
            'Category': category,
            'Quantity': quantity,
            'Unit_Price': unit_price,
            'Total_Amount': total_amount
        })
    
    return pd.DataFrame(sample_data)

def save_sample_data():
    """Save sample data to CSV file"""
    df = generate_sample_data()
    filename = f"sample_sales_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(filename, index=False)
    print(f"Sample data saved to {filename}")
    print(f"Generated {len(df)} sample transactions")
    print("\nSample data preview:")
    print(df.head(10))
    return filename

if __name__ == "__main__":
    save_sample_data()
