import requests
url = "https://people.sc.fsu.edu/-jburkardt/data/csv/hw_200.csv"
response = requests.get(url)
open("data.csv","wb").write(response.content)
print("CSV file downloaded as data.csv")

import pandas as pd
data = {
    "Name":["<PERSON>","<PERSON>","<PERSON>","<PERSON>"],
    "Age":[25,30,35,40],
}
a=df = pd.DataFrame(data)
print(df)
df.to_csv("data.csv",index=False)
print("CSV file created as data.csv")