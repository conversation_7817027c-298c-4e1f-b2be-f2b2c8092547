import tkinter as tk
from tkinter import ttk
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
from datetime import datetime, timedelta

class ReportsWindow:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Financial Reports")
        self.window.geometry("1000x700")
        self.window.configure(bg='#f0f0f0')
        
        self.create_interface()
        
    def create_interface(self):
        # Title
        title_label = tk.Label(self.window, text="Financial Reports & Analytics", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=20)
        
        if not MATPLOTLIB_AVAILABLE:
            error_label = tk.Label(self.window, 
                                  text="Matplotlib not installed. Install with: pip install matplotlib",
                                  font=('Arial', 12), fg='red', bg='#f0f0f0')
            error_label.pack(pady=20)
            
            # Show text-based reports instead
            self.create_text_reports()
        else:
            # Notebook for different reports
            notebook = ttk.Notebook(self.window)
            notebook.pack(fill='both', expand=True, padx=20, pady=10)
            
            # Account Balance Report
            balance_frame = ttk.Frame(notebook)
            notebook.add(balance_frame, text="Account Balances")
            self.create_balance_chart(balance_frame)
            
            # Monthly Spending Report
            spending_frame = ttk.Frame(notebook)
            notebook.add(spending_frame, text="Monthly Spending")
            self.create_spending_chart(spending_frame)
            
            # Income vs Expenses
            income_frame = ttk.Frame(notebook)
            notebook.add(income_frame, text="Income vs Expenses")
            self.create_income_expense_chart(income_frame)
    
    def create_text_reports(self):
        # Text-based reports when matplotlib is not available
        text_frame = tk.Frame(self.window, bg='#f0f0f0')
        text_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        report_text = tk.Text(text_frame, font=('Courier', 10))
        report_text.pack(fill='both', expand=True)
        
        sample_report = """
FINANCIAL REPORTS - TEXT VERSION
================================

ACCOUNT BALANCES:
-----------------
Checking Account:    $2,450.75
Savings Account:     $15,230.50
Credit Card:         -$1,245.30
Investment Account:  $8,750.25
                     ----------
Total Assets:        $25,186.20

MONTHLY SPENDING TREND:
-----------------------
January:    $2,500
February:   $2,800
March:      $2,200
April:      $3,100
May:        $2,900
June:       $2,600

INCOME VS EXPENSES:
-------------------
Monthly Income:      $5,000
Housing:            -$1,500
Food:               -$800
Transportation:     -$600
Entertainment:      -$400
Savings:            -$700
                    --------
Net Income:         $1,000

To see graphical reports, install matplotlib:
pip install matplotlib
        """
        
        report_text.insert(1.0, sample_report)
        report_text.config(state='disabled')
        
    def create_balance_chart(self, parent):
        # Sample data
        accounts = ['Checking', 'Savings', 'Credit Card', 'Investment']
        balances = [2450.75, 15230.50, -1245.30, 8750.25]
        colors = ['#3498db', '#27ae60', '#e74c3c', '#9b59b6']
        
        fig, ax = plt.subplots(figsize=(8, 6))
        bars = ax.bar(accounts, balances, color=colors)
        
        ax.set_title('Account Balances', fontsize=16, fontweight='bold')
        ax.set_ylabel('Balance ($)', fontsize=12)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Add value labels on bars
        for bar, balance in zip(bars, balances):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + (50 if height > 0 else -100),
                   f'${balance:,.2f}', ha='center', va='bottom' if height > 0 else 'top')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_spending_chart(self, parent):
        # Sample monthly spending data
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        spending = [2500, 2800, 2200, 3100, 2900, 2600]
        
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.plot(months, spending, marker='o', linewidth=2, markersize=8, color='#e74c3c')
        ax.fill_between(months, spending, alpha=0.3, color='#e74c3c')
        
        ax.set_title('Monthly Spending Trend', fontsize=16, fontweight='bold')
        ax.set_ylabel('Amount Spent ($)', fontsize=12)
        ax.set_xlabel('Month', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Add value labels
        for i, v in enumerate(spending):
            ax.text(i, v + 50, f'${v:,}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_income_expense_chart(self, parent):
        # Sample data
        categories = ['Income', 'Housing', 'Food', 'Transportation', 'Entertainment', 'Savings']
        amounts = [5000, -1500, -800, -600, -400, -700]
        colors = ['#27ae60', '#e74c3c', '#e74c3c', '#e74c3c', '#e74c3c', '#3498db']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        
        # Bar chart
        bars = ax1.bar(categories, amounts, color=colors)
        ax1.set_title('Income vs Expenses', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Amount ($)', fontsize=12)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        for bar, amount in zip(bars, amounts):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (50 if height > 0 else -100),
                    f'${abs(amount):,}', ha='center', va='bottom' if height > 0 else 'top')
        
        plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
        
        # Pie chart for expenses only
        expense_categories = ['Housing', 'Food', 'Transportation', 'Entertainment']
        expense_amounts = [1500, 800, 600, 400]
        expense_colors = ['#e74c3c', '#f39c12', '#9b59b6', '#1abc9c']
        
        ax2.pie(expense_amounts, labels=expense_categories, colors=expense_colors, 
                autopct='%1.1f%%', startangle=90)
        ax2.set_title('Expense Breakdown', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
