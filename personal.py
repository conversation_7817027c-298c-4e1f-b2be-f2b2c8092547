import streamlit as st
import pandas as pd
from datetime import datetime, date

# Set page configuration
st.set_page_config(
    page_title="Personal Finance Tracker",
    page_icon="💰",
    layout="wide"
)

# Initialize session state for storing expenses
if 'expenses' not in st.session_state:
    st.session_state.expenses = []

# Title and header
st.title("💰 Personal Finance Tracker")
st.markdown("---")

# Create two columns for layout
col1, col2 = st.columns([1, 2])

with col1:
    st.header("📝 Add New Expense")

    # Expense category selectbox
    category = st.selectbox(
        "Select Expense Category:",
        options=[
            "Food & Dining",
            "Transportation",
            "Shopping",
            "Entertainment",
            "Bills & Utilities",
            "Healthcare",
            "Education",
            "Travel",
            "Groceries",
            "Other"
        ],
        index=0
    )

    # Amount input
    amount = st.number_input(
        "Enter Amount ($):",
        min_value=0.01,
        max_value=10000.00,
        value=0.01,
        step=0.01,
        format="%.2f"
    )

    # Date picker
    expense_date = st.date_input(
        "Choose Date:",
        value=date.today(),
        max_value=date.today()
    )

    # Optional notes
    notes = st.text_area(
        "Add Notes (Optional):",
        placeholder="Enter any additional details about this expense...",
        height=100
    )

    # Add expense button
    if st.button("➕ Add Expense", type="primary"):
        if amount > 0:
            # Create new expense entry
            new_expense = {
                "Date": expense_date,
                "Category": category,
                "Amount": amount,
                "Notes": notes if notes else "No notes"
            }

            # Add to session state
            st.session_state.expenses.append(new_expense)
            st.success(f"✅ Added ${amount:.2f} expense for {category}")
            st.rerun()
        else:
            st.error("❌ Please enter a valid amount greater than $0")

with col2:
    st.header("📊 Expense Summary")

    if st.session_state.expenses:
        # Convert to DataFrame
        df = pd.DataFrame(st.session_state.expenses)
        df['Date'] = pd.to_datetime(df['Date'])

        # Display total expenses
        total_expenses = df['Amount'].sum()
        st.metric("💸 Total Expenses", f"${total_expenses:.2f}")

        # Display expenses by category
        st.subheader("📈 Expenses by Category")
        category_summary = df.groupby('Category')['Amount'].sum().sort_values(ascending=False)

        # Create bar chart using streamlit
        st.bar_chart(category_summary)

        # Display recent expenses table
        st.subheader("📋 Recent Expenses")
        df_display = df.sort_values('Date', ascending=False).copy()
        df_display['Date'] = df_display['Date'].dt.strftime('%Y-%m-%d')
        df_display['Amount'] = df_display['Amount'].apply(lambda x: f"${x:.2f}")

        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True
        )

        # Clear all expenses button
        if st.button("🗑️ Clear All Expenses", type="secondary"):
            st.session_state.expenses = []
            st.success("✅ All expenses cleared!")
            st.rerun()

    else:
        st.info("📝 No expenses recorded yet. Add your first expense using the form on the left!")

        # Display sample data info
        st.markdown("""
        ### 🎯 How to use this tracker:
        1. **Select Category**: Choose from predefined expense categories
        2. **Enter Amount**: Input the expense amount in dollars
        3. **Pick Date**: Select when the expense occurred
        4. **Add Notes**: Optional details about the expense
        5. **Click Add**: Save the expense to your tracker

        ### 📊 Features:
        - ✅ Real-time expense tracking
        - ✅ Visual charts and summaries
        - ✅ Category-wise breakdown
        - ✅ Date-wise expense history
        - ✅ Notes for detailed tracking
        """)

# Sidebar with additional information
st.sidebar.header("💡 Finance Tips")
st.sidebar.markdown("""
### 🎯 Smart Spending Tips:
- 📝 Track every expense, no matter how small
- 🎯 Set monthly budgets for each category
- 📊 Review your spending patterns regularly
- 💡 Look for areas where you can cut costs
- 🏦 Save at least 20% of your income

### 📱 Categories Explained:
- **Food & Dining**: Restaurants, takeout, coffee
- **Transportation**: Gas, public transport, rideshare
- **Shopping**: Clothes, electronics, non-essentials
- **Bills & Utilities**: Rent, electricity, internet
- **Healthcare**: Medical, pharmacy, insurance
- **Education**: Books, courses, training
- **Travel**: Vacation, business trips
- **Groceries**: Food shopping, household items
""")

# Footer
st.markdown("---")
st.markdown(
    "<div style='text-align: center; color: gray;'>"
    "💰 Personal Finance Tracker | Built with Streamlit"
    "</div>",
    unsafe_allow_html=True
)