import tkinter as tk
from tkinter import ttk, messagebox

class AccountManager:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Account Manager")
        self.window.geometry("800x600")
        self.window.configure(bg='#f0f0f0')
        
        self.accounts = [
            {"id": "001", "name": "Checking Account", "type": "Checking", "balance": 2450.75},
            {"id": "002", "name": "Savings Account", "type": "Savings", "balance": 15230.50},
            {"id": "003", "name": "Credit Card", "type": "Credit", "balance": -1245.30},
            {"id": "004", "name": "Investment Account", "type": "Investment", "balance": 8750.25}
        ]
        
        self.create_interface()
        
    def create_interface(self):
        # Title
        title_label = tk.Label(self.window, text="Account Management", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=20)
        
        # Button frame
        button_frame = tk.Frame(self.window, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Add Account", command=self.add_account,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Edit Account", command=self.edit_account,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Delete Account", command=self.delete_account,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        # Account list
        self.create_account_list()
        
    def create_account_list(self):
        # Treeview for account list
        columns = ('ID', 'Name', 'Type', 'Balance')
        self.tree = ttk.Treeview(self.window, columns=columns, show='headings', height=15)
        
        # Define headings
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # Insert data
        for account in self.accounts:
            self.tree.insert('', tk.END, values=(
                account['id'], account['name'], account['type'], f"${account['balance']:,.2f}"
            ))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(self.window, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=20)
        scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=20)
    
    def add_account(self):
        messagebox.showinfo("Add Account", "Add account dialog coming soon!")
        
    def edit_account(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select an account to edit")
            return
        messagebox.showinfo("Edit Account", "Edit account dialog coming soon!")
        
    def delete_account(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select an account to delete")
            return
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this account?"):
            self.tree.delete(selected[0])
